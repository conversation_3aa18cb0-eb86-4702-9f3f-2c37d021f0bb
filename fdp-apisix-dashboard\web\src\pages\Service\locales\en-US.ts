/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.service.steps.stepTitle.basicInformation': 'Basic',
  'page.service.steps.stepTitle.pluginConfig': 'Plugin',
  'page.service.steps.stepTitle.preview': 'Preview',
  'page.service.list': 'Service List',
  'page.service.description':
    'A service consists of a combination of public plugin configuration and upstream target information in a route. Services are associated with Routes and Upstreams, and a service can correspond to a set of upstream nodes and can be bound by multiple routes.',
  'page.service.fields.name.required': 'Please enter service name',
  'page.service.fields.hosts': 'Hosts',
  'page.service.fields.hosts.placeholder': 'Please enter service hosts',
  'page.service.create': 'Create Service',
  'page.service.configure': 'Configure Service',
};
