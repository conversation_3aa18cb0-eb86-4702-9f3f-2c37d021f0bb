/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  'component.user.login': '<PERSON><PERSON><PERSON>',
  'component.user.loginMethodPassword': 'Kullanıcı Adı & Şifre',
  'component.user.loginMethodPassword.username': '<PERSON>llan<PERSON>cı Adı',
  'component.user.loginMethodPassword.password': 'Şifre',
  'component.user.loginMethodPassword.inputUsername': '<PERSON><PERSON><PERSON><PERSON> kullanıcı adı girin',
  'component.user.loginMethodPassword.inputPassword': 'Lütfen şifre girin',
  'component.user.loginMethodPassword.incorrectPassword': 'Kullanıcı adı veya şifre yanlış',
  'component.user.loginMethodPassword.fieldInvalid': 'Lütfen kullanıcı adı ve şifreyi kontrol edin',
  'component.user.loginMethodPassword.success': 'Giriş Başarılı',
  'component.user.loginMethodPassword.changeDefaultAccount':
    'Kullanıcı adı ve şifreyi değiştirmek nasıl yapılır?',
  'component.user.loginMethodPassword.modificationMethod':
    'Lütfen /api/conf/conf.yaml dosyasındaki kullanıcılar alanını değiştirin',
};
