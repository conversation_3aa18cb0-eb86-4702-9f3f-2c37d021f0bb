import re
import json
from app.ai.chart_recommender import recommend_chart_from_ai
from app.integrations.superset_client import SupersetClient
from app.utils.sql_utils import (
    get_dataset_columns, get_metric_column_id, extract_metric_alias_from_sql
)

superset = SupersetClient()

def recommend_chart(data):
    sql = data.get("sql")
    schema_hint = data.get("schema_hint")
    ai_model = data.get("ai_model")
    recommendation = recommend_chart_from_ai(sql, schema_hint, ai_model)
    return {
        "success": True,
        "recommend": recommendation,
        "sql": sql
    }

def create_chart_service(data):
    chart_type = data.get('chart_type')
    # G<PERSON>i từng hàm sinh params payload theo loại chart
    if chart_type == 'table':
        params = build_table_params(data)
    elif chart_type == 'echarts_timeseries_bar':
        params = build_bar_params(data)
    elif chart_type == 'pie':
        params = build_pie_params(data)
    else:
        return {"success": False, "message": f"Chart type {chart_type} chưa hỗ trợ!"}, 400

    # G<PERSON><PERSON> xuống integration tạo chart
    result, status_code = superset.create_chart(chart_type, params)
    if status_code in [200, 201]:
        return {
            "success": True,
            "chart_id": result.get("id"),
            "chart_url": f"{superset.base_url}/explore/?slice_id={result.get('id')}",
            "result": result
        }, 200
    else:
        msg = result.get("message") if isinstance(result, dict) and "message" in result else result
        return {"success": False, "message": msg}, status_code

# --- Hàm build params cho từng loại chart ---

def build_table_params(data):
    """
    Chuẩn hóa payload params cho chart Table để gửi cho Superset API.
    Dùng trong kiến trúc 3-layer, gọi từ service.
    """
    chart_name = data.get("chart_name", "Auto Chart")
    viz_type = data.get("viz_type", "table")
    dataset_id = data.get("dataset_id")
    columns = data.get("columns", [])
    row_limit = data.get("row_limit", 20)

    if not dataset_id:
        # Có thể raise exception hoặc trả về None tùy workflow của bạn
        raise ValueError("Missing dataset_id")

    params = {
        "all_columns": columns,
        "row_limit": row_limit,
        "query_mode": "raw"
    }

    # Trả về dict để integration wrap thành payload gửi lên Superset
    return {
        "chart_name": chart_name,
        "viz_type": viz_type,
        "dataset_id": dataset_id,
        "params": params
    }


def build_bar_params(data):
    """
    Build payload params cho chart Bar gửi lên Superset API (3-layer).
    """
    chart_name = data.get("chart_name", "Auto Chart")
    viz_type = data.get("viz_type", data.get("chart_type", "echarts_timeseries_bar"))
    datasource_id = data.get("dataset_id")

    # Validate bắt buộc
    if not datasource_id:
        raise ValueError("Missing dataset_id")

    # Lấy toàn bộ column của dataset một lần
    columns = get_dataset_columns(datasource_id)
    dataset_column_names = [col.get("column_name") for col in columns]

    # Xử lý robust metric_column
    metric_col_raw = data.get("metric_column") or data.get("metrics")
    if isinstance(metric_col_raw, list):
        metric_column = metric_col_raw[0] if metric_col_raw else None
    else:
        metric_column = metric_col_raw
    metric_column = str(metric_column).strip() if metric_column else None

    # Clean metric_column: bỏ dấu ), lấy tên thực nếu là SUM(xxx)
    if metric_column and metric_column.endswith(')'):
        metric_column = metric_column[:-1]
    if metric_column and '(' in metric_column and ')' in metric_column:
        match = re.search(r"\((.*?)\)", metric_column)
        if match:
            metric_column = match.group(1).strip()

    # Nếu metric_column không có trong dataset, thử lấy alias từ SQL gốc
    if metric_column not in dataset_column_names and "sql" in data:
        metric_alias = extract_metric_alias_from_sql(data["sql"])
        if metric_alias and metric_alias in dataset_column_names:
            metric_column = metric_alias

    metric_agg = data.get("metric_agg", "SUM")
    metric_column_id = get_metric_column_id(datasource_id, metric_column)
    groupby = data.get("groupby", [])
    x_axis = data.get("x_axis", "name")
    row_limit = data.get("row_limit", 10000)

    if not (datasource_id and metric_column and metric_column_id):
        raise ValueError("Missing required fields: dataset_id, metric_column, metric_column_id")

    # Build adhoc metric đúng chuẩn Superset
    metric = {
        "expressionType": "SIMPLE",
        "column": {
            "column_name": metric_column,
            "id": metric_column_id
        },
        "aggregate": metric_agg,
        "label": f"{metric_agg}({metric_column})"
    }

    params = {
        "datasource": f"{datasource_id}__table",
        "viz_type": viz_type,
        "x_axis": x_axis,
        "time_grain_sqla": "P1D",
        "x_axis_sort_asc": True,
        "metrics": [metric],
        "groupby": groupby,
        "adhoc_filters": [],
        "order_desc": True,
        "row_limit": row_limit,
        "truncate_metric": True,
        "show_empty_columns": True,
        "comparison_type": "values",
        "annotation_layers": [],
        "forecastPeriods": 10,
        "forecastInterval": 0.8,
        "orientation": "vertical",
        "x_axis_title_margin": 15,
        "y_axis_title_margin": 30,
        "y_axis_title_position": "Left",
        "sort_series_type": "sum",
        "color_scheme": "supersetColors",
        "time_shift_color": True,
        "only_total": True,
        "show_legend": True,
        "legendType": "scroll",
        "legendOrientation": "top",
        "x_axis_time_format": "smart_date",
        "xAxisLabelInterval": "auto",
        "y_axis_format": "SMART_NUMBER",
        "y_axis_bounds": [None, None],
        "truncateXAxis": True,
        "rich_tooltip": True,
        "showTooltipTotal": True,
        "tooltipTimeFormat": "smart_date",
        "extra_form_data": {},
        "dashboards": [],
    }

    return {
        "chart_name": chart_name,
        "viz_type": viz_type,
        "dataset_id": datasource_id,
        "params": params
    }

def build_pie_params(data):
    """
    Build payload params cho Pie Chart gửi lên Superset API (3-layer).
    """
    chart_name = data.get("chart_name", "Auto Pie Chart")
    viz_type = data.get("viz_type", data.get("chart_type", "pie"))
    datasource_id = data.get("dataset_id")
    if not datasource_id:
        raise ValueError("Missing dataset_id")

    # Lấy toàn bộ column của dataset một lần
    columns = get_dataset_columns(datasource_id)
    dataset_column_names = [col.get("column_name") for col in columns]

    # Lấy metric_column, robust với mọi trường hợp AI trả về
    metric_col_raw = data.get("metric_column") or data.get("metrics")
    if isinstance(metric_col_raw, list):
        metric_column = metric_col_raw[0] if metric_col_raw else None
    else:
        metric_column = metric_col_raw
    metric_column = str(metric_column).strip() if metric_column else None

    # Clean: bỏ dấu ) cuối, lấy tên thực nếu là biểu thức SUM(xxx)
    if metric_column and metric_column.endswith(')'):
        metric_column = metric_column[:-1]
    if metric_column and '(' in metric_column and ')' in metric_column:
        match = re.search(r"\((.*?)\)", metric_column)
        if match:
            metric_column = match.group(1).strip()

    # Nếu metric_column không có trong dataset, thử lấy alias từ SQL gốc
    if metric_column not in dataset_column_names and "sql" in data:
        metric_alias = extract_metric_alias_from_sql(data["sql"])
        if metric_alias and metric_alias in dataset_column_names:
            metric_column = metric_alias

    metric_agg = data.get("metric_agg", "SUM")
    metric_column_id = get_metric_column_id(datasource_id, metric_column)
    groupby = data.get("groupby") or data.get("columns") or []
    if isinstance(groupby, str):
        groupby = [groupby]
    row_limit = data.get("row_limit", 10000)

    if not (datasource_id and metric_column and metric_column_id):
        raise ValueError("Missing required fields: dataset_id, metric_column, metric_column_id")

    # Chuẩn hóa adhoc metric đúng format Superset UI
    metric = {
        "expressionType": "SIMPLE",
        "column": {
            "column_name": metric_column,
            "id": metric_column_id
        },
        "aggregate": metric_agg,
        "label": f"{metric_agg}({metric_column})"
    }

    params = {
        "datasource": f"{datasource_id}__table",
        "viz_type": viz_type,
        "groupby": groupby,
        "metric": metric,
        "adhoc_filters": [],
        "row_limit": row_limit,
        "sort_by_metric": True,
        "color_scheme": "supersetColors",
        "show_labels_threshold": 5,
        "show_legend": True,
        "legendType": "scroll",
        "legendOrientation": "top",
        "label_type": "key",
        "number_format": "SMART_NUMBER",
        "date_format": "smart_date",
        "show_labels": True,
        "labels_outside": True,
        "outerRadius": 70,
        "innerRadius": 30,
        "extra_form_data": {},
        "dashboards": []
    }

    return {
        "chart_name": chart_name,
        "viz_type": viz_type,
        "dataset_id": datasource_id,
        "params": params
    }

