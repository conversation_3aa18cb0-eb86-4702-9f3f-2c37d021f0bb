/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.tagSelect.expand': 'Expand',
  'component.tagSelect.collapse': 'Collapse',
  'component.tagSelect.all': 'All',
  'component.global.search': 'Search',
  'component.global.reset': 'Reset',
  'component.global.confirm': 'Confirm',
  'component.global.format': 'Format',
  'component.global.document': 'Document',
  'component.global.enable': 'Enable',
  'component.global.disable': 'Disable',
  'component.global.scope': 'Scope',
  'component.global.example': 'Example',
  'component.global.data.editor': 'Raw Data Editor',
  'component.global.delete': 'Delete',
  'component.global.cancel': 'Cancel',
  'component.global.submit': 'Submit',
  'component.global.create': 'Create',
  'component.global.add': 'Add',
  'component.global.save': 'Save',
  'component.global.edit': 'Configure',
  'component.global.view': 'View',
  'component.global.duplicate': 'Duplicate',
  'component.global.manage': 'Manage',
  'component.global.update': 'Update',
  'component.global.get': 'Get',
  'component.global.edit.plugin': 'Configure plugin',
  'component.global.loading': 'Loading',
  'component.global.list': 'List',
  'component.global.description': 'Description',
  'component.global.description.required': 'Please enter the description',
  'component.global.labels': 'Labels',
  'component.global.version': 'Version',
  'component.global.operation': 'Operation',
  'component.status.success': 'Successfully',
  'component.status.fail': 'Failed',
  'component.global.popconfirm.title.delete': 'Are you sure to delete this record ?',
  'component.global.notification.success.message.deleteSuccess': 'Deleted Successfully',
  'component.global.create.consumer.success': 'Create Consumer Successfully',
  'component.global.delete.consumer.success': 'Delete Consumer Successfully',
  'component.global.delete.routes.success': 'Delete Route Successfully',
  'component.global.edit.consumer.success': 'Edit Consumer Successfully',

  'component.global.steps.stepTitle.basicInformation': 'Basic Information',
  'component.global.steps.stepTitle.preview': 'Preview',
  'component.global.steps.stepTitle.pluginConfig': 'Plugin Config',

  'component.global.pleaseEnter': 'Please Enter',
  'component.global.pleaseChoose': 'Please Choose',
  'component.global.pleaseCheck': 'Please Check',

  'component.global.input.ruleMessage.name':
    'Only letters, numbers, - and _ are supported, and can only begin with letters',

  'component.global.connectionTimeout': 'Connection Timeout',
  'component.global.sendTimeout': 'Send Timeout',
  'component.global.receiveTimeout': 'Receive Timeout',
  'component.global.name': 'Name',
  'component.global.id': 'ID',
  'component.global.updateTime': 'Update Time',
  'component.global.form.itemExtraMessage.nameGloballyUnique': 'Name should be globally unique',
  'component.global.input.placeholder.description':
    'Please enter the description for this route, max 256 characters',
  // User component
  'component.user.loginByPassword': 'Username & Password',
  'component.user.login': 'Login',

  'component.document': 'Document',
  'component.label-manager': 'Label Manager',

  'component.global.noConfigurationRequired': 'No configuration required',
  'component.global.copy': 'Copy',
  'component.global.copySuccess': 'Copy Successfully ',
  'component.global.copyFail': 'Copy Failed',

  'component.global.invalidYaml': 'Invalid Yaml data',
  'component.global.invalidJson': 'Invalid Json data',
};
