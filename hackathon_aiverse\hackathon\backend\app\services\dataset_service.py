from app.integrations.superset_client import SupersetClient
import re

superset = SupersetClient()

def auto_create_dataset_service(data):
    # Check required fields
    required_fields = ['database', 'schema', 'table_name']
    for field in required_fields:
        if not data.get(field):
            return {"success": False, "message": f"Missing required field: {field}"}, 400

    # Tự động chuyển dấu cách thành dấu gạch dưới
    table_name = data.get("table_name", "")
    new_table_name = re.sub(r"\s+", "_", table_name)
    if table_name != new_table_name:
        print(f"[INFO] Đã tự động chuyển table_name từ '{table_name}' thành '{new_table_name}'")
    data["table_name"] = new_table_name

    # Chỉ truyền sql nếu muốn tạo virtual dataset (và Superset phải hỗ trợ version này)
    dataset_payload = {
        "database": data["database"],
        "schema": data["schema"],
        "table_name": data["table_name"]
    }
    if data.get("sql"):
        dataset_payload["sql"] = data["sql"]
        dataset_payload["is_managed_externally"] = False
        dataset_payload["external_url"] = None

    # Gọi Superset API qua integration
    result, status_code = superset.create_dataset(dataset_payload)

    # Chuẩn hóa trả về
    if status_code in [200, 201]:
        return {"success": True, "dataset": result}, status_code
    else:
        msg = result.get("msg") if isinstance(result, dict) and "msg" in result else result
        return {"success": False, "message": msg}, status_code
