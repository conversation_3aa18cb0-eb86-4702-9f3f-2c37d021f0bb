/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.proto.list': 'Proto 列表',
  'page.proto.list.description':
    'Protocol Buffers 是 Google 用于序列化结构化数据的框架，它具有语言中立、平台中立、可扩展机制的特性，您只需定义一次数据的结构化方式，然后就可以使用各种语言通过特殊生成的源代码轻松地将结构化数据写入和读取各种数据流。Protocol Buffers 列表包含了已创建的 proto 文件，在启用 grpc-transcode 插件时可配置 ID 读取对应的 proto 文件内容。',
  'page.proto.list.edit': '配置',
  'page.proto.list.confirm.delete': '确定删除该条记录吗？',
  'page.proto.list.confirm': '确定',
  'page.proto.list.cancel': '取消',
  'page.proto.list.delete.successfully': '删除记录成功',
  'page.proto.list.delete': '删除',

  'page.proto.id.tooltip': '.proto 文件的 id',

  'page.proto.desc': '描述',
  'page.proto.desc.tooltip': '.proto 文件的描述',

  'page.proto.content': '内容',
  'page.proto.content.tooltip': '.proto 文件的内容',

  'page.proto.drawer.create': '创建 proto',
  'page.proto.drawer.edit': '配置 proto',
  'page.proto.drawer.create.successfully': '创建 proto 成功',
  'page.proto.drawer.edit.successfully': '配置 proto 成功',
  'page.proto.drawer.delete.successfully': '删除 proto 成功',
};
