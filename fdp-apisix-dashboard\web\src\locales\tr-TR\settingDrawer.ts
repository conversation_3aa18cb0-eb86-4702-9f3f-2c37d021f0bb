/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'app.setting.pagestyle': 'Sayfa Stili',
  'app.setting.pagestyle.dark': 'Karanlık',
  'app.setting.pagestyle.light': 'Açık',
  'app.setting.content-width': 'İçerik genişliği',
  'app.setting.content-width.fixed': 'Sabit',
  'app.setting.content-width.fluid': 'Sıcak',
  'app.setting.themecolor': 'Tema rengi',
  'app.setting.themecolor.dust': 'Kum',
  'app.setting.themecolor.volcano': 'Volkan',
  'app.setting.themecolor.sunset': '<PERSON><PERSON><PERSON>bat<PERSON>m<PERSON>',
  'app.setting.themecolor.cyan': 'Açık Mavi',
  'app.setting.themecolor.green': 'Yeşil',
  'app.setting.themecolor.daybreak': 'Tan Yeri',
  'app.setting.themecolor.geekblue': 'Gizemli Mavi',
  'app.setting.themecolor.purple': 'Altın Moru',
  'app.setting.navigationmode': 'Navigasyon modu',
  'app.setting.sidemenu': 'Kenar menü',
  'app.setting.topmenu': 'Üst menü',
  'app.setting.fixedheader': 'Sabit başlık',
  'app.setting.fixedsidebar': 'Sabit kenar menü',
  'app.setting.fixedsidebar.hint': 'Kenar menüyü sabitlemeyi seçin',
  'app.setting.hideheader': 'Başlığı gizle',
  'app.setting.hideheader.hint': 'Başlığı gizlemeyi seçin',
  'app.setting.othersettings': 'Diğer ayarlar',
  'app.setting.weakmode': 'Zayıf mod',
  'app.setting.copy': 'Ayarları Kopyala',
  'app.setting.copyinfo':
    'Kopyalama başarılı,Lütfen, defaultSettings ayalarını src/models/setting.js da değiştirin',
  'app.setting.production.hint': 'Bu panel, sadece geliştirme modunda görüntülenebilir.',
};
