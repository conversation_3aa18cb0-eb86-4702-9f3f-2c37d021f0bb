from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_community.utilities import SQLDatabase
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnablePassthrough
import re
from sqlalchemy import text, create_engine
import requests
import json
from flasgger import Swagger   # <-- Thêm dòng này

# Load env
load_dotenv()
FPT_MODEL = os.getenv("FPT_MODEL", "fpt-4-32k")
FPT_API_KEY = os.getenv("FPT_API_KEY")
FPT_API_BASE = os.getenv("FPT_API_BASE")

SUPERSET_URL = os.getenv("SUPERSET_URL")
SUPERSET_FE_URL = os.getenv("SUPERSET_FE_URL")

def read_token_from_file(key: str, filename: str = "superset_tokens.txt") -> str:
    if not os.path.exists(filename):
        raise FileNotFoundError(f"File {filename} không tồn tại")

    with open(filename, "r") as f:
        for line in f:
            if line.startswith(f"{key}="):
                return line.strip().split("=", 1)[1]

    raise KeyError(f"Không tìm thấy {key} trong file {filename}")

def trino_engine(user, password, host, port, catalog, schema):
    uri = f"trino://{user}:{password}@{host}:{port}/{catalog}/{schema}"
    return create_engine(uri)

def init_database(user: str, password: str, host: str, port: str, catalog: str, schema: str) -> SQLDatabase:
    db_uri = f"trino://{user}:{password}@{host}:{port}/{catalog}/{schema}"
    return SQLDatabase.from_uri(db_uri)

def clean_response(text: str) -> str:
    text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL | re.IGNORECASE)
    text = re.sub(r"```.*?```", "", text, flags=re.DOTALL)
    return text.strip()

def cache_schema_per_table(db: SQLDatabase, db_name: str):
    engine = db._engine
    with engine.connect() as conn:
        # Tạo bảng cache nếu chưa có
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS table_schemas (
                db_name VARCHAR,
                table_name VARCHAR,
                ddl VARCHAR
            )
        """))
        # Lấy danh sách tất cả các bảng
        table_names = [row[0] for row in conn.execute(text("SHOW TABLES"))]
        for table_name in table_names:
            ddl_result = conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
            ddl_row = ddl_result.fetchone()
            if ddl_row and ddl_row[0]:
                ddl = ddl_row[0]
                # Thử update, nếu chưa có thì insert
                update_result = conn.execute(
                    text("""
                        UPDATE table_schemas SET ddl = :ddl
                        WHERE db_name = :db_name AND table_name = :table_name
                    """),
                    {"db_name": db_name, "table_name": table_name, "ddl": ddl.strip()}
                )
                if update_result.rowcount == 0:
                    conn.execute(
                        text("""
                            INSERT INTO table_schemas (db_name, table_name, ddl)
                            VALUES (:db_name, :table_name, :ddl)
                        """),
                        {"db_name": db_name, "table_name": table_name, "ddl": ddl.strip()}
                    )


def get_cached_schema(db: SQLDatabase, db_name: str) -> str:
    try:
        with db._engine.connect() as conn:
            result = conn.execute(text("SELECT ddl FROM table_schemas WHERE db_name = :db"), {"db": db_name})
            rows = result.fetchall()
            return "\n\n".join(row[0] for row in rows) if rows else ""
    except Exception as e:
        return f"❌ Schema load error: {e}"

def fpt_complete(messages: list, ai_model=None) -> str:
    llm = ChatOpenAI(
        model=ai_model or FPT_MODEL,   # Ưu tiên model truyền vào, fallback env
        openai_api_key=FPT_API_KEY,
        openai_api_base=FPT_API_BASE,
        temperature=0.0,
    )
    response = llm.invoke(messages)
    return response.content.strip()


def extract_sql_only(response: str) -> str:
    response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r"```.*?```", "", response, flags=re.DOTALL)
    lines = [line.strip().rstrip(';') for line in response.splitlines() if line.strip()]
    candidates = [line for line in lines if line.lower().startswith("select") and " from " in line.lower() and len(line) > 10]
    if candidates:
        return candidates[-1]
    select_lines = [line for line in lines if line.lower().startswith("select")]
    if select_lines:
        return max(select_lines, key=len)
    m = re.search(r"(SELECT\s+.+FROM\s+.+?;?)", response, re.IGNORECASE | re.DOTALL)
    if m:
        return m.group(1).strip().rstrip(';')
    return response.strip()

def get_sql_chain(db, db_name, ai_model=None):
    def chain_fn(vars):
        schema = get_cached_schema(db, db_name)
        # Nếu cache chưa có, mới tạo cache_schema_per_table 1 lần đầu
        if not schema or schema.startswith("❌"):
            print(f"[CACHE MISS] Caching schema for {db_name}")
            cache_schema_per_table(db, db_name)
            schema = get_cached_schema(db, db_name)
        else:
            print(f"[CACHE HIT] Using cached schema for {db_name}")
        chat_history = vars["chat_history"]
        user_query = vars["question"]
        messages = []
        sys_prompt = (
            "You are a SQL expert. Based on the schema below, output ONLY a valid SQL SELECT statement that answers the user's question. "
            "Do NOT explain, do NOT add markdown, do NOT add comments. Only the SQL, one line.\n"
            "- Always use the exact column and table names from the schema (e.g., use products.name not product_name).\n"
            "- If there are relationships via columns like customer_id, product_id, order_id, always use JOIN statements based on these keys.\n"
            "- Use JOIN ... ON ... syntax, not implicit join.\n"
            "For example, to get all products for a customer: "
            "SELECT p.name FROM customers c JOIN orders o ON c.id = o.customer_id "
            "JOIN order_details od ON o.id = od.order_id JOIN products p ON od.product_id = p.id WHERE c.name = 'Alice';\n"
            f"<SCHEMA>{schema}</SCHEMA>"
        )
        messages.append(SystemMessage(content=sys_prompt))
        for msg in chat_history:
            if isinstance(msg, HumanMessage):
                messages.append(HumanMessage(content=msg.content))
            elif isinstance(msg, AIMessage):
                messages.append(AIMessage(content=msg.content))
        messages.append(HumanMessage(content=user_query))
        response = fpt_complete(messages, ai_model=ai_model)
        print("\n==== RAW LLM RESPONSE ====\n", response)
        sql = extract_sql_only(response)
        print("\n==== EXTRACTED SQL ====\n", sql)
        if len(sql) < 15 or " from " not in sql.lower():
            raise Exception(f"❌ SQL generate error: Model did not return a valid SQL statement: {sql}")
        return sql
    return RunnablePassthrough.assign(query=chain_fn)

# ---- FLASK API for Superset ----
app = Flask(__name__)
CORS(app)
swagger = Swagger(app)         # <-- Thêm dòng này

@app.route('/api/nl2sql', methods=['POST'])
def nl2sql_api():
    """
    Sinh câu lệnh SQL từ câu hỏi tiếng Việt.

    ---
    tags:
      - NL2SQL
    summary: Sinh SQL từ câu hỏi tự nhiên
    description: |
      Nhận đầu vào là câu hỏi tiếng Việt (hoặc tiếng Anh) và trả về truy vấn SQL phù hợp. Cho phép tùy chọn truyền thông tin database, schema, table và model AI.
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: Dữ liệu gửi lên gồm câu hỏi và các thông tin liên quan database/schema/table/model.
        required: true
        schema:
          type: object
          properties:
            question:
              type: string
              example: "Tỉ lệ đơn hàng theo trạng thái"
            database:
              type: string
              example: "trino"
            schema:
              type: string
              example: "lakehouse_db"
            table:
              type: string
              example: "orders"
            ai_model:
              type: string
              example: "fpt-4-32k"
    responses:
      200:
        description: Câu SQL sinh ra từ AI
        schema:
          type: object
          properties:
            sql:
              type: string
              example: |
                SELECT status, COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders) AS percentage
                FROM orders
                GROUP BY status
    """
    data = request.json
    question = data.get('question', '')
    database = data.get('database', '')       # e.g. postgresql
    schema = data.get('schema', '')           # e.g. public
    table = data.get('table', '')             # e.g. birth_names
    ai_model = data.get('ai_model')  # e.g. fpt-4-32k
    print("data: ", data)
    print("ai_model: ", ai_model)
    # Bạn cần tự mapping db info (user/pass/host/port/catalog/schema) theo config thực tế!
    # Ví dụ, nếu dùng 1 Trino connection mặc định:
    TRINO_USER = os.getenv("TRINO_USER", "admin")
    TRINO_PASSWORD = os.getenv("TRINO_PASSWORD", "admin")
    TRINO_HOST = os.getenv("TRINO_HOST", "localhost")
    TRINO_PORT = os.getenv("TRINO_PORT", "8080")
    TRINO_CATALOG = os.getenv("TRINO_CATALOG", "hivecatalog")
    TRINO_SCHEMA =  os.getenv("TRINO_SCHEMA", "lakehouse_db") or schema

    db = init_database(
        TRINO_USER,
        TRINO_PASSWORD,
        TRINO_HOST,
        TRINO_PORT,
        TRINO_CATALOG,
        TRINO_SCHEMA
    )

    db_name = TRINO_SCHEMA
    chat_history = []  # Nếu muốn giữ history thì bổ sung logic cho từng phiên (user)
    try:
        sql_chain = get_sql_chain(db, db_name, ai_model=ai_model)
        sql = sql_chain.invoke({
            "question": question,
            "chat_history": chat_history,
        })["query"]
        print("[NL2SQL] Question:", question)
        print("[NL2SQL] SQL:", sql)
    except Exception as e:
        sql = f"-- ERROR: {e}"
        print("[NL2SQL] ERROR:", e)

    return jsonify({'sql': sql})


@app.route('/api/refresh_schema', methods=['POST'])
def refresh_schema():
    """
    Làm mới (refresh) cache schema các bảng trong database.

    ---
    tags:
      - Schema
    summary: Refresh schema cache
    description: |
      Gọi API này để làm mới cache toàn bộ DDL schema của các bảng vào bảng `table_schemas`.  
      Database/schema đang được cấu hình cứng trong backend (ví dụ: Trino + lakehouse_db).
    consumes:
      - application/json
    responses:
      200:
        description: Làm mới schema thành công
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: true
            message:
              type: string
              example: "Schema refreshed!"
      500:
        description: Lỗi khi làm mới schema
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Connection error: database unreachable"
    """

    """
    API để refresh schema (cache lại toàn bộ ddl vào table_schemas)
    Hardcode luôn database/schema (ví dụ Trino + lakehouse_db)
    """
    print("[REFRESH SCHEMA] Refreshing schema cache...")
    
    # Hardcode info (giống như ở nl2sql_api)
    TRINO_USER = os.getenv("TRINO_USER", "admin")
    TRINO_PASSWORD = os.getenv("TRINO_PASSWORD", "admin")
    TRINO_HOST = os.getenv("TRINO_HOST", "localhost")
    TRINO_PORT = os.getenv("TRINO_PORT", "8080")
    TRINO_CATALOG = os.getenv("TRINO_CATALOG", "hivecatalog")
    TRINO_SCHEMA = os.getenv("TRINO_SCHEMA", "lakehouse_db")

    try:
        db = init_database(
            TRINO_USER,
            TRINO_PASSWORD,
            TRINO_HOST,
            TRINO_PORT,
            TRINO_CATALOG,
            TRINO_SCHEMA
        )
        cache_schema_per_table(db, TRINO_SCHEMA)
        return jsonify({"success": True, "message": "Schema refreshed!"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

def auto_create_table_chart(data):
    print("data: ", data)

    chart_name = data.get("chart_name", "Auto Chart")
    viz_type = data.get("viz_type", "table")
    dataset_id = data.get("dataset_id")  # Chú ý: đây là ID của dataset, không phải database
    columns = data.get("columns")
    row_limit = data.get("row_limit", 20)

    if not dataset_id:
        return jsonify({"success": False, "message": "Missing dataset_id"}), 400

    SUPERSET_API_TOKEN = read_token_from_file("SUPERSET_ACCESS_TOKEN")

    headers = {
        "Authorization": f"Bearer {SUPERSET_API_TOKEN}",
        "Content-Type": "application/json"
    }

    params = {
        "all_columns": columns,
        "row_limit": row_limit,
        "query_mode": "raw"
    }

    payload = {
        "slice_name": chart_name,
        "viz_type": viz_type,
        "datasource_id": dataset_id,
        "datasource_type": "table",
        "params": json.dumps(params)
    }

    try:
        r = requests.post(f"{SUPERSET_URL}/api/v1/chart/", json=payload, headers=headers)
        if r.status_code == 201:
            chart_id = r.json().get("id")
            chart_url = f"{SUPERSET_FE_URL}/explore/?slice_id={chart_id}"
            return jsonify({"success": True, "chart_url": chart_url, "chart_id": chart_id})
        else:
            return jsonify({
                "success": False,
                "message": r.text,
                "payload": payload
            }), 500
    except Exception as e:
        return jsonify({
            "success": False,
            "message": str(e)
        }), 500

@app.route('/api/auto_create_dataset', methods=['POST'])
def auto_create_dataset():
    """
    Tự động tạo dataset trên Superset từ câu SQL.

    ---
    tags:
      - Dataset
    summary: Auto create Superset dataset
    description: |
      Tạo mới một dataset trong Superset dựa trên truy vấn SQL và thông tin database/schema/table_name cung cấp.
      Trả về thông tin dataset nếu thành công, hoặc thông báo lỗi nếu thất bại.
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: Thông tin cần thiết để tạo dataset
        required: true
        schema:
          type: object
          properties:
            database:
              type: integer
              example: 1
              description: ID của database trong Superset
            schema:
              type: string
              example: "lakehouse_db"
              description: Tên schema
            sql:
              type: string
              example: "SELECT id, name, total FROM sales"
              description: Truy vấn SQL nguồn cho dataset
            table_name:
              type: string
              example: "auto_dataset_2024"
              description: Tên dataset sẽ được tạo
    responses:
      200:
        description: Tạo dataset thành công
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: true
            dataset:
              type: object
              description: Thông tin dataset vừa tạo
      400:
        description: Request không hợp lệ
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Missing required fields"
      500:
        description: Lỗi tạo dataset
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Superset API error: Unauthorized"
    """

    data = request.json

    print("data: ", data)

    database = data.get("database", 1)
    schema = data.get("schema")
    sql = data.get("sql")
    table_name = data.get("table_name")
    # SUPERSET_API_TOKEN = os.getenv("SUPERSET_ACCESS_TOKEN")
    SUPERSET_API_TOKEN = read_token_from_file("SUPERSET_ACCESS_TOKEN")
    # access_token = data.get("access_token", SUPERSET_API_TOKEN)  # FE truyền lên hoặc lấy env
    # print("SUPERSET_API_TOKEN: ", os.getenv("SUPERSET_ACCESS_TOKEN"))
    payload = {
        "database": database,
        "catalog": None,
        "schema": schema,
        "sql": sql,
        "table_name": table_name,
        "is_managed_externally": False,
        "external_url": None
    }

    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {SUPERSET_API_TOKEN}",
        "Origin": SUPERSET_URL,
        "Referer": f"{SUPERSET_URL}/sqllab/",
    }

    url = f"{SUPERSET_URL}/api/v1/dataset/"
    resp = requests.post(url, json=payload, headers=headers)

    if resp.status_code in [200, 201]:
        return jsonify({"success": True, "dataset": resp.json()})
    else:
        return jsonify({"success": False, "message": resp.text}), resp.status_code

def fpt_recommend_chart(sql, schema_hint=None, ai_model=None):
    """
    Gọi FPT AI Model để khuyến nghị loại chart và params từ câu SQL.
    schema_hint: Truyền thêm DDL nếu muốn model phân tích kỹ hơn.
    """
    # Prompt có ví dụ, đảm bảo model trả về đúng cấu trúc JSON dễ parse
    prompt = f"""
    Khi sinh JSON cấu hình chart, TUYỆT ĐỐI KHÔNG được sử dụng "columns": ["*"] hay "SELECT *".
    Luôn liệt kê đầy đủ tất cả các tên trường cụ thể (column name) của bảng.
    Ví dụ đúng: "columns": ["id", "name", "email"]
    Ví dụ sai: "columns": ["*"]

    Nếu SQL chứa SELECT *, hãy tra cứu schema để lấy tên tất cả các trường và liệt kê hết vào "columns".

    Hãy phân tích truy vấn SQL dưới đây và trả về loại biểu đồ Superset phù hợp nhất và các tham số cấu hình chính cho chart đó dưới dạng object JSON. 
    Chỉ trả về JSON, không giải thích. 

    Nếu là biểu đồ dạng bảng, trả về:
    {{"chart_type": "table", "columns": ["col1", "col2", ...]}}

    Nếu là biểu đồ cột/thanh, trả về:
    {{"chart_type": "echarts_timeseries_bar", "x_axis": "col_x", "metrics": ["col_y"], "groupby": [...], "columns": ["col1", "col2", ...]}}

    Nếu là biểu đồ tròn:
    {{"chart_type": "pie", "columns": ["col1"], "metrics": ["col_y"]}}

    Ví dụ:
    SQL: SELECT country, COUNT(*) FROM sales GROUP BY country
    JSON: {{"chart_type": "pie", "columns": ["country"], "metrics": ["COUNT(*)"]}}

    SQL: SELECT created_at, SUM(amount) FROM revenue GROUP BY created_at
    JSON: {{"chart_type": "echarts_timeseries_bar", "x_axis": "created_at", "metrics": ["SUM(amount)"], "groupby": []}}

    SQL: SELECT * FROM users
    JSON: {{"chart_type": "table", "columns": ["id", "name", "email", ...]}}  # <-- Ghi rõ các trường

    Dưới đây là SQL cần phân tích:
    SQL: {sql}
    """

    if schema_hint:
        prompt += f"\nSchema:\n{schema_hint}\n"

    messages = [
        {"role": "user", "content": prompt}
    ]

    # Chuẩn hóa headers, endpoint theo config FPT
    FPT_API_BASE = os.getenv("FPT_API_BASE")
    FPT_API_KEY = os.getenv("FPT_API_KEY")
    FPT_MODEL = ai_model or os.getenv("FPT_MODEL")

    headers = {
        "Authorization": f"Bearer {FPT_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": FPT_MODEL,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 256
    }
    # Nếu API bạn dùng là chuẩn OpenAI-compatible (Chat format)
    resp = requests.post(
        f"{FPT_API_BASE}/v1/chat/completions",
        json=payload, headers=headers, timeout=20
    )
    resp.raise_for_status()
    content = resp.json()["choices"][0]["message"]["content"]
    return content

@app.route('/api/recommend_chart', methods=['POST'])
def recommend_chart():
    """
    Gợi ý loại biểu đồ và cấu hình dựa trên câu SQL.

    ---
    tags:
      - Chart
    summary: Recommend chart type from SQL
    description: |
      Nhận đầu vào là một câu truy vấn SQL và (tùy chọn) schema, trả về gợi ý loại biểu đồ phù hợp nhất cho Superset và cấu hình các tham số chính (dạng JSON).
      Được tích hợp AI model để phân tích tự động.
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: Dữ liệu gồm SQL và các thông tin liên quan (nếu có)
        required: true
        schema:
          type: object
          properties:
            sql:
              type: string
              example: "SELECT status, COUNT(*) FROM orders GROUP BY status"
              description: Câu SQL cần phân tích để gợi ý chart
            schema_hint:
              type: string
              example: "CREATE TABLE orders (id INT, status VARCHAR, ...)"
              description: (Tùy chọn) DDL hoặc schema để AI phân tích sâu hơn
            ai_model:
              type: string
              example: "fpt-4-32k"
              description: (Tùy chọn) Model AI sử dụng
    responses:
      200:
        description: Gợi ý chart thành công
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: true
            recommend:
              type: object
              description: Cấu hình chart Superset dạng JSON
              example:
                chart_type: pie
                columns: ["status"]
                metrics: ["COUNT(*)"]
            raw:
              type: string
              description: Raw response trả về từ AI model
              example: |
                {"chart_type": "pie", "columns": ["status"], "metrics": ["COUNT(*)"]}
            sql:
              type: string
              description: Câu SQL đầu vào
              example: "SELECT status, COUNT(*) FROM orders GROUP BY status"
      400:
        description: Thiếu SQL
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Missing sql"
      500:
        description: Lỗi xử lý hoặc lỗi AI
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "AI service unavailable"
    """    
    data = request.json or {}

    print("recommend_chart data: ", data)

    sql = data.get("sql")
    schema_hint = data.get("schema_hint")  # Nếu muốn gửi DDL hoặc schema
    ai_model = data.get("ai_model")
    if not sql:
        return jsonify({"success": False, "message": "Missing sql"}), 400
    try:
        result_raw = fpt_recommend_chart(sql, schema_hint=schema_hint, ai_model=ai_model)
        import re
        import json as _json

        try:
            # Strip markdown formatting if present (e.g., ```json\n...\n```)
            if result_raw.strip().startswith("```json"):
                result_raw = re.sub(r"^```json\s*|\s*```$", "", result_raw.strip())

            chart_obj = _json.loads(result_raw)
            chart_obj = clean_chart_obj_fields(chart_obj)

        except Exception as e:
            print("❌ JSON parse failed:", e)
            chart_obj = {}
        
        print("chart_obj: ", chart_obj)
        print("result_raw: ", result_raw) 
        
        # Thêm sql vào response
        return jsonify({
            "success": True,
            "recommend": chart_obj,
            "raw": result_raw,
            "sql": sql            # Truyền nguyên sql gốc ra ngoài
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/create_chart', methods=['POST'])
def create_chart():
    """
    Tạo chart Superset theo loại và cấu hình đầu vào.
    ---
    tags:
      - Chart
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - chart_type
            - dataset_id
          properties:
            chart_type:
              type: string
              description: Loại chart ("table", "echarts_timeseries_bar", "pie")
              example: "pie"
            chart_name:
              type: string
              example: "Tỉ lệ đơn hàng theo trạng thái"
            dataset_id:
              type: integer
              example: 3
            columns:
              type: array
              items:
                type: string
              example: ["status"]
            metrics:
              type: array
              items:
                type: string
              example: ["COUNT(*)"]
            groupby:
              type: array
              items:
                type: string
              example: ["status"]
            row_limit:
              type: integer
              example: 100
    responses:
      200:
        description: Chart created successfully
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: true
            chart_url:
              type: string
              example: "http://localhost:9000/explore/?slice_id=123"
            chart_id:
              type: integer
              example: 123
      400:
        description: Bad request
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Chart type pie chưa hỗ trợ!"
      500:
        description: Internal server error
        schema:
          type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Failed to create chart"
    """

    data = request.json or {}

    print("create_chart data: ", data)

    chart_type = data.get('chart_type')
    print("create_chart chart_type: ", chart_type)
    # ... lấy các tham số khác như dataset_id, columns, metrics...

    if chart_type == 'table':
        return auto_create_table_chart(data)  # Gọi function tạo table chart
    elif chart_type == 'echarts_timeseries_bar':
        return auto_create_bar_chart(data)    # Gọi function tạo bar chart
    elif chart_type == 'pie':
        return auto_create_pie_chart(data)    # Gọi function tạo pie chart
    # ... thêm elif cho các loại chart khác nếu muốn mở rộng
    else:
        return jsonify({"success": False, "message": f"Chart type {chart_type} chưa hỗ trợ!"}), 400

def strip_alias(field_name):
    return field_name.split(".")[-1] if isinstance(field_name, str) else field_name

def clean_chart_obj_fields(chart_obj):
    for key in ['columns', 'groupby', 'metrics']:
        if key in chart_obj:
            chart_obj[key] = [strip_alias(col) for col in chart_obj[key]]

    if "x_axis" in chart_obj:
        chart_obj["x_axis"] = strip_alias(chart_obj["x_axis"])

    return chart_obj


def build_metric_adhoc(column_name, aggregate="SUM"):
    return {
        "expressionType": "SIMPLE",
        "column": {"column_name": column_name},
        "aggregate": aggregate,
        "label": f"{aggregate}({column_name})"
    }

def extract_metric_alias_from_sql(sql: str):
    """
    Trích xuất alias metric từ câu SQL ví dụ: SELECT ... SUM(x) AS total_x ...
    """
    m = re.search(r'\bAS\s+(\w+)', sql, re.IGNORECASE)
    return m.group(1) if m else None

def get_dataset_columns(datasource_id: int):
    token = read_token_from_file("SUPERSET_ACCESS_TOKEN")
    fe_url = os.getenv("SUPERSET_FE_URL", "http://localhost:9000")
    headers = {"Authorization": f"Bearer {token}"}
    url = f"{fe_url}/api/v1/dataset/{datasource_id}"
    r = requests.get(url, headers=headers)
    if r.status_code != 200:
        raise Exception(f"Failed to fetch dataset info: {r.text}")
    data = r.json()
    return data.get("result", {}).get("columns", [])

def get_metric_column_id(datasource_id: int, column_name: str) -> int:
    """
    Lấy ID của column (hoặc metric) từ Superset dataset, robust với mọi kiểu đầu vào AI.
    """
    columns = get_dataset_columns(datasource_id)
    target_col = column_name.strip() if column_name else None

    # Normalize tên cột: bỏ dấu ngoặc, lấy tên thật nếu là SUM(xxx) hoặc AVG(xxx), bỏ dấu ')' ở cuối.
    if target_col and target_col.endswith(')'):
        target_col = target_col[:-1]
    if target_col and "(" in target_col and ")" in target_col:
        match = re.search(r"\((.*?)\)", target_col)
        if match:
            target_col = match.group(1).strip()

    for col in columns:
        if col.get("column_name") == target_col:
            return col.get("id")

    print(f"[DEBUG] Columns in dataset {datasource_id}:", [col.get("column_name") for col in columns])
    raise Exception(f"Column '{column_name}' (normalized as '{target_col}') not found in dataset {datasource_id}")

def auto_create_bar_chart(data):
    """
    Tạo bar chart với mọi payload AI trả về (robust với metric_column bị lỗi định dạng và tự động lấy alias).
    """
    import re
    SUPERSET_API_TOKEN = read_token_from_file("SUPERSET_ACCESS_TOKEN")
    print("auto_create_bar_chart data: ", data)
    chart_name = data.get("chart_name", "Auto Chart")
    viz_type = data.get("viz_type", data.get("chart_type", "echarts_timeseries_bar"))
    datasource_id = data.get("dataset_id")

    # Lấy toàn bộ column của dataset một lần
    columns = get_dataset_columns(datasource_id)
    dataset_column_names = [col.get("column_name") for col in columns]

    # Nhận metric_column từ nhiều nguồn, robust với mọi trường hợp AI trả về.
    metric_col_raw = data.get("metric_column") or data.get("metrics")
    if isinstance(metric_col_raw, list):
        metric_column = metric_col_raw[0]
    else:
        metric_column = metric_col_raw
    metric_column = str(metric_column).strip() if metric_column else None

    # Clean: bỏ dấu ) cuối, lấy tên thực nếu là biểu thức SUM(xxx)
    if metric_column and metric_column.endswith(')'):
        metric_column = metric_column[:-1]
    if metric_column and '(' in metric_column and ')' in metric_column:
        match = re.search(r"\((.*?)\)", metric_column)
        if match:
            metric_column = match.group(1).strip()
    print(f"[DEBUG] metric_column after clean = '{metric_column}'")

    # Nếu metric_column không có trong dataset, thử lấy alias từ SQL gốc
    if metric_column not in dataset_column_names and "sql" in data:
        metric_alias = extract_metric_alias_from_sql(data["sql"])
        if metric_alias and metric_alias in dataset_column_names:
            metric_column = metric_alias
            print(f"[DEBUG] Auto switch metric_column to alias from SQL: '{metric_column}'")

    metric_agg = data.get("metric_agg", "SUM")
    metric_column_id = get_metric_column_id(datasource_id, metric_column)
    groupby = data.get("groupby", [])
    x_axis = data.get("x_axis", "name")
    row_limit = data.get("row_limit", 10000)

    print("metric_column_id: ", metric_column_id)

    if not (datasource_id and metric_column and metric_column_id):
        return jsonify({"success": False, "message": "Missing required fields: dataset_id, metric_column, metric_column_id"}), 400

    # Tạo adhoc metric như UI Superset
    metric = {
        "expressionType": "SIMPLE",
        "column": {
            "column_name": metric_column,
            "id": metric_column_id
        },
        "aggregate": metric_agg,
        "label": f"{metric_agg}({metric_column})"
    }

    params = {
        "datasource": f"{datasource_id}__table",
        "viz_type": viz_type,
        "x_axis": x_axis,
        "time_grain_sqla": "P1D",
        "x_axis_sort_asc": True,
        "metrics": [metric],
        "groupby": groupby,
        "adhoc_filters": [],
        "order_desc": True,
        "row_limit": row_limit,
        "truncate_metric": True,
        "show_empty_columns": True,
        "comparison_type": "values",
        "annotation_layers": [],
        "forecastPeriods": 10,
        "forecastInterval": 0.8,
        "orientation": "vertical",
        "x_axis_title_margin": 15,
        "y_axis_title_margin": 30,
        "y_axis_title_position": "Left",
        "sort_series_type": "sum",
        "color_scheme": "supersetColors",
        "time_shift_color": True,
        "only_total": True,
        "show_legend": True,
        "legendType": "scroll",
        "legendOrientation": "top",
        "x_axis_time_format": "smart_date",
        "xAxisLabelInterval": "auto",
        "y_axis_format": "SMART_NUMBER",
        "y_axis_bounds": [None, None],
        "truncateXAxis": True,
        "rich_tooltip": True,
        "showTooltipTotal": True,
        "tooltipTimeFormat": "smart_date",
        "extra_form_data": {},
        "dashboards": [],
    }

    query_context = {
        "datasource": {"id": datasource_id, "type": "table"},
        "force": False,
        "queries": [
            {
                "filters": [],
                "extras": {"time_grain_sqla": "P1D", "having": "", "where": ""},
                "applied_time_extras": {},
                "columns": [{
                    "timeGrain": "P1D",
                    "columnType": "BASE_AXIS",
                    "sqlExpression": x_axis,
                    "label": x_axis,
                    "expressionType": "SQL"
                }],
                "metrics": [metric],
                "orderby": [[metric, False]],
                "annotation_layers": [],
                "row_limit": row_limit,
                "series_columns": [],
                "series_limit": 0,
                "order_desc": True,
                "url_params": {},
                "custom_params": {},
                "custom_form_data": {},
                "time_offsets": [],
                "post_processing": [
                    {"operation": "pivot", "options": {
                        "index": [x_axis], "columns": [], "aggregates": {f"{metric_agg}({metric_column})": {"operator": "mean"}}, "drop_missing_columns": False
                    }},
                    {"operation": "flatten"}
                ]
            }
        ],
        "form_data": dict(params, **{
            "force": False,
            "result_format": "json",
            "result_type": "full"
        }),
        "result_format": "json",
        "result_type": "full"
    }

    payload = {
        "params": json.dumps(params, ensure_ascii=False),
        "slice_name": chart_name,
        "viz_type": viz_type,
        "datasource_id": datasource_id,
        "datasource_type": "table",
        "dashboards": [],
        "owners": [],
        "query_context": json.dumps(query_context, ensure_ascii=False)
    }

    headers = {
        "Authorization": f"Bearer {SUPERSET_API_TOKEN}",
        "Content-Type": "application/json"
    }

    r = requests.post(f"{os.getenv('SUPERSET_URL')}/api/v1/chart/", json=payload, headers=headers)
    if r.status_code == 201:
        chart_id = r.json().get("id")
        chart_url = f"{os.getenv('SUPERSET_FE_URL')}/explore/?slice_id={chart_id}"
        return jsonify({"success": True, "chart_url": chart_url, "chart_id": chart_id})
    else:
        return jsonify({"success": False, "message": r.text, "payload": payload}), 500

def auto_create_pie_chart(data):
    """
    Tạo Pie Chart với payload linh hoạt (robust với AI trả về, tự lấy metric_column_id, hỗ trợ alias metric).
    """
    import re, os, json, requests
    SUPERSET_API_TOKEN = read_token_from_file("SUPERSET_ACCESS_TOKEN")
    chart_name = data.get("chart_name", "Auto Pie Chart")
    viz_type = data.get("viz_type", data.get("chart_type", "pie"))
    datasource_id = data.get("dataset_id")

    # Lấy toàn bộ column của dataset một lần
    columns = get_dataset_columns(datasource_id)
    dataset_column_names = [col.get("column_name") for col in columns]

    # Lấy metric_column, robust với mọi trường hợp AI trả về
    metric_col_raw = data.get("metric_column") or data.get("metrics")
    if isinstance(metric_col_raw, list):
        metric_column = metric_col_raw[0]
    else:
        metric_column = metric_col_raw
    metric_column = str(metric_column).strip() if metric_column else None

    # Clean: bỏ dấu ) cuối, lấy tên thực nếu là biểu thức SUM(xxx)
    if metric_column and metric_column.endswith(')'):
        metric_column = metric_column[:-1]
    if metric_column and '(' in metric_column and ')' in metric_column:
        match = re.search(r"\((.*?)\)", metric_column)
        if match:
            metric_column = match.group(1).strip()

    # Nếu metric_column không có trong dataset, thử lấy alias từ SQL gốc
    if metric_column not in dataset_column_names and "sql" in data:
        metric_alias = extract_metric_alias_from_sql(data["sql"])
        if metric_alias and metric_alias in dataset_column_names:
            metric_column = metric_alias

    metric_agg = data.get("metric_agg", "SUM")
    metric_column_id = get_metric_column_id(datasource_id, metric_column)
    groupby = data.get("groupby") or data.get("columns") or []
    if isinstance(groupby, str):
        groupby = [groupby]
    row_limit = data.get("row_limit", 10000)

    # Chuẩn hóa adhoc metric đúng format Superset UI
    metric = {
        "expressionType": "SIMPLE",
        "column": {
            "column_name": metric_column,
            "id": metric_column_id
        },
        "aggregate": metric_agg,
        "label": f"{metric_agg}({metric_column})"
    }

    params = {
        "datasource": f"{datasource_id}__table",
        "viz_type": viz_type,
        "groupby": groupby,
        "metric": metric,
        "adhoc_filters": [],
        "row_limit": row_limit,
        "sort_by_metric": True,
        "color_scheme": "supersetColors",
        "show_labels_threshold": 5,
        "show_legend": True,
        "legendType": "scroll",
        "legendOrientation": "top",
        "label_type": "key",
        "number_format": "SMART_NUMBER",
        "date_format": "smart_date",
        "show_labels": True,
        "labels_outside": True,
        "outerRadius": 70,
        "innerRadius": 30,
        "extra_form_data": {},
        "dashboards": []
    }

    query_context = {
        "datasource": {"id": datasource_id, "type": "table"},
        "force": False,
        "queries": [
            {
                "filters": [],
                "extras": {"having": "", "where": ""},
                "applied_time_extras": {},
                "columns": groupby,
                "metrics": [metric],
                "orderby": [[metric, False]],
                "annotation_layers": [],
                "row_limit": row_limit,
                "series_limit": 0,
                "order_desc": True,
                "url_params": {},
                "custom_params": {},
                "custom_form_data": {},
                "post_processing": [
                    {"operation": "contribution", "options": {
                        "columns": [metric["label"]],
                        "rename_columns": [f"{metric['label']}__contribution"]
                    }}
                ]
            }
        ],
        "form_data": dict(params, **{
            "force": False,
            "result_format": "json",
            "result_type": "full"
        }),
        "result_format": "json",
        "result_type": "full"
    }

    payload = {
        "params": json.dumps(params, ensure_ascii=False),
        "slice_name": chart_name,
        "viz_type": viz_type,
        "datasource_id": datasource_id,
        "datasource_type": "table",
        "dashboards": [],
        "owners": [],
        "query_context": json.dumps(query_context, ensure_ascii=False)
    }

    headers = {
        "Authorization": f"Bearer {SUPERSET_API_TOKEN}",
        "Content-Type": "application/json"
    }
    superset_url = f"{os.getenv('SUPERSET_URL')}/api/v1/chart/"
    r = requests.post(superset_url, json=payload, headers=headers)
    if r.status_code == 201:
        chart_id = r.json().get("id")
        chart_url = f"{os.getenv('SUPERSET_FE_URL')}/explore/?slice_id={chart_id}"
        return jsonify({"success": True, "chart_url": chart_url, "chart_id": chart_id})
    else:
        return jsonify({"success": False, "message": r.text, "payload": payload}), 500


if __name__ == '__main__':
    app.run(port=5000, debug=True)