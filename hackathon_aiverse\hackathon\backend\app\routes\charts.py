from flask import Blueprint, request, jsonify
from app.services.chart_service import recommend_chart, create_chart_service

charts_bp = Blueprint("charts", __name__, url_prefix="/api")

@charts_bp.route('/recommend_chart', methods=['POST'])
def recommend():
    data = request.json
    result = recommend_chart(data)
    return jsonify(result)

@charts_bp.route('/create_chart', methods=['POST'])
def create_chart():
    data = request.json
    result, status_code = create_chart_service(data)
    return jsonify(result), status_code
