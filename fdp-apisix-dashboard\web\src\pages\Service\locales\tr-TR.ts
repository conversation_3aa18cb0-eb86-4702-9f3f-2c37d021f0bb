/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.service.steps.stepTitle.basicInformation': 'Temel',
  'page.service.steps.stepTitle.pluginConfig': 'Eklenti',
  'page.service.steps.stepTitle.preview': 'Önizleme',
  'page.service.list': 'Hizmet Listesi',
  'page.service.description':
    '<PERSON>ir hizmet, bir rotadaki genel eklenti yapılandırması ve yukarı akış hedef bilgilerinin bir kombinasyonundan oluşur. <PERSON>z<PERSON>ler, Yollar ve Yukarı Akışlar ile ilişkilidir ve bir hizmet, bir dizi yukarı akış düğümüne karşılık gelebilir ve birden çok yolla bağlanabilir.',
  'page.service.fields.name.required': 'Lütfen hizmet adını girin',
  'page.service.fields.hosts': 'Hostlar',
  'page.service.fields.hosts.placeholder': 'Lütfen hizmet hostlarını girin',
  'page.service.create': 'Hizmet oluştur',
  'page.service.configure': 'Hizmeti yapılandır',
};
