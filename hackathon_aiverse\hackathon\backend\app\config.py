import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    TRINO_USER = os.getenv("TRINO_USER")
    TRINO_PASSWORD = os.getenv("TRINO_PASSWORD")
    TRINO_HOST = os.getenv("TRINO_HOST")
    TRINO_PORT = os.getenv("TRINO_PORT")
    TRINO_CATALOG = os.getenv("TRINO_CATALOG")
    TRINO_SCHEMA = os.getenv("TRINO_SCHEMA")
    SUPERSET_URL = os.getenv("SUPERSET_URL")
    SUPERSET_API_TOKEN = os.getenv("SUPERSET_API_TOKEN")
    FPT_MODEL = os.getenv("FPT_MODEL")
    FPT_API_KEY = os.getenv("FPT_API_KEY")
    FPT_API_BASE = os.getenv("FPT_API_BASE")

settings = Settings()
