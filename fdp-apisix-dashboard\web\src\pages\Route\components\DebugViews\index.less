/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@import '~antd/es/style/themes/default.less';

.routeDebugDraw {
  :global {
    .ant-card-body {
      padding: 0;
    }

    .ant-drawer-body {
      padding: 16px;
    }

    .ant-form-item {
      margin-bottom: 8px;
    }
  }

  .authForm {
    display: flex;
    margin: 16px;

    :global {
      .ant-radio-wrapper {
        display: block;
        height: 30px;
        line-height: 30px;
      }

      .ant-radio-group {
        margin-right: 16px;
        border-right: 1px solid #e0e0e0;
      }
    }
  }
}
