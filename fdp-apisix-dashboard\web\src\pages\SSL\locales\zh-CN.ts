/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.ssl.form.itemLabel.expireTime': '过期时间',
  'page.ssl.form.itemLabel.cert': '证书',
  'page.ssl.form.itemRuleMessage.certValueLength': '证书内容至少需要128个字符',
  'page.ssl.form.itemLabel.privateKey': '私钥',
  'page.ssl.form.itemRuleMessage.privateKeyLength': '私钥 至少需要128个字符',

  'page.ssl.button.uploadCert': '上传证书',

  'page.ssl.form.itemLabel.way': '方式',
  'page.ssl.select.placeholder.selectCreateWays': '请选择创建方式',
  'page.ssl.selectOption.input': '输入',
  'page.ssl.upload': '上传',

  'page.ssl.notification.updateCertEnableStatusSuccessfully': '更新证书启用状态成功',
  'page.ssl.list': '证书列表',
  'page.ssl.list.expirationTime': '过期时间',
  'page.ssl.list.ifEnable': '是否启用',
  'page.ssl.list.periodOfValidity': '有效期',
  'page.ssl.steps.stepTitle.completeCertInfo': '完善证书信息',
  'component.ssl.removeSSLSuccess': '删除证书成功',
  'component.ssl.removeSSLItemModalContent': '确定要删除该证书吗？',

  'component.ssl.description':
    '证书被网关用于处理加密请求，它将与 SNI 关联，并与路由中主机名绑定。',
  'component.ssl.fields.cert.required': '请输入证书内容',
  'component.ssl.fields.key.required': '请输入私钥内容',
};
