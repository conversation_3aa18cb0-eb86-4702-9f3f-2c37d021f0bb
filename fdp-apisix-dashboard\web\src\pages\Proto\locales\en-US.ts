/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.proto.list': 'Proto List',
  'page.proto.list.description':
    "Protocol buffers are Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data.You define how you want your data to be structured once, then you can use special generated source code to easily write and read your structured data to and from a variety of data streams and using a variety of languages.The protocol buffers list contains the created proto files. When the grpc transcode plug-in is enabled, the ID can be configured to read the contents of the corresponding proto files.",
  'page.proto.list.edit': 'Configure',
  'page.proto.list.confirm.delete': 'Are you sure to delete ?',
  'page.proto.list.confirm': 'Confirm',
  'page.proto.list.cancel': 'Cancel',
  'page.proto.list.delete.successfully': 'Delete proto Successfully',
  'page.proto.list.delete': 'Delete',
  'page.proto.id.tooltip': ".proto file's id",

  'page.proto.desc': 'description',
  'page.proto.desc.tooltip': ".proto file's description",

  'page.proto.content': 'content',
  'page.proto.content.tooltip': ".proto file's content",

  'page.proto.drawer.create': 'Create proto',
  'page.proto.drawer.edit': 'Configure proto',
  'page.proto.drawer.create.successfully': 'Create proto Successfully',
  'page.proto.drawer.edit.successfully': 'Configure proto Successfully',
  'page.proto.drawer.delete.successfully': 'Delete proto Successfully',
};
