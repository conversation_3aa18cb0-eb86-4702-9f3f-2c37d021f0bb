/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.select {
  padding: 12px 24px;
  background-color: #fff;
}

.wrap {
  width: 100%;
  margin: 0 auto;
  padding: 16px 24px;
  background-color: #fff;
}

.table {
  width: 100%;
  color: rgb(0 0 0 / 45%);
  border-collapse: collapse;

  th,
  td {
    padding: 12px 8px;
  }

  td:nth-child(2) {
    text-align: right;
  }

  tr:nth-child(odd) {
    background-color: #f3f4f5;
  }
}
