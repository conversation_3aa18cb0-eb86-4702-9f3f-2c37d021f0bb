/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.service.steps.stepTitle.basicInformation': '基本信息',
  'page.service.steps.stepTitle.pluginConfig': '插件配置',
  'page.service.steps.stepTitle.preview': '预览',
  'page.service.list': '服务列表',
  'page.service.description':
    '服务由路由中公共的插件配置、上游目标信息组合而成。服务与路由、上游关联，一个服务可对应一组上游节点、可被多条路由绑定。',
  'page.service.fields.name.required': '请输入服务名称',
  'page.service.fields.hosts': '域名',
  'page.service.fields.hosts.placeholder': '请输入服务域名',
  'page.service.create': '创建服务',
  'page.service.configure': '配置服务',
};
