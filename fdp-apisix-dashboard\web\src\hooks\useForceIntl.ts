/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { useEffect } from 'react';
import { getIntl } from 'umi';

/**
 * Force convert some texts with i18n
 */
const useForceIntl = () => {
  useEffect(() => {
    const hasProTable = Boolean(document.querySelector('.ant-pro-table-search'));

    if (!hasProTable) {
      return;
    }

    const { locale } = getIntl();
    if (locale === 'zh-CN') {
      return;
    }

    // NOTE: i18n in https://procomponents.ant.design/components/table/ is not working
    const i18nMapper = [
      ['//span[text()="查 询"]', 'Search'],
      ['//span[text()="重 置"]', 'Reset'],
    ];

    i18nMapper.forEach(([XPathExpression, targetText]) => {
      const ele = document.evaluate(XPathExpression, document).iterateNext() as HTMLElement;
      if (!ele) {
        return;
      }
      ele.innerText = targetText;
    });
  }, []);
};

export default useForceIntl;
