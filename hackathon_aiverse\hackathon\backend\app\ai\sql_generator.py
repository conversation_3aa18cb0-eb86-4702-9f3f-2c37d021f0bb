from langchain_core.messages import HumanMessage, SystemMessage
from app.ai.fpt_llm import fpt_complete
from app.utils.sql_utils import extract_sql_only

def generate_sql(question, schema, ai_model=None):
    # sys_prompt = f"You are SQL expert. Schema:\n{schema}"
    sys_prompt = (
        "You are a SQL expert. Based on the schema below, output ONLY a valid SQL SELECT statement that answers the user's question. "
        "Do NOT explain, do NOT add markdown, do NOT add comments. Only the SQL, one line.\n"
        "- Always use the exact column and table names from the schema (e.g., use products.name not product_name).\n"
        "- If there are relationships via columns like customer_id, product_id, order_id, always use JOIN statements based on these keys.\n"
        "- Use JOIN ... ON ... syntax, not implicit join.\n"
        "For example, to get all products for a customer: "
        "SELECT p.name FROM customers c JOIN orders o ON c.id = o.customer_id "
        "JOIN order_details od ON o.id = od.order_id JOIN products p ON od.product_id = p.id WHERE c.name = 'Alice';\n"
        f"<SCHEMA>{schema}</SCHEMA>"
    )
    messages = [
        SystemMessage(content=sys_prompt),
        HumanMessage(content=question)
    ]
    response = fpt_complete(messages, ai_model=ai_model)
    sql = extract_sql_only(response)
    return sql
