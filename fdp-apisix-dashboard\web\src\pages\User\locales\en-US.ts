/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  'component.user.login': 'Login',
  'component.user.loginMethodPassword': 'Username & Password',
  'component.user.loginMethodPassword.username': 'Username',
  'component.user.loginMethodPassword.password': 'Password',
  'component.user.loginMethodPassword.inputUsername': 'Please input username',
  'component.user.loginMethodPassword.inputPassword': 'Please input password',
  'component.user.loginMethodPassword.incorrectPassword': 'Incorrect username or password',
  'component.user.loginMethodPassword.fieldInvalid': 'Please check username and password',
  'component.user.loginMethodPassword.success': 'Login Success',
  'component.user.loginMethodPassword.changeDefaultAccount': 'How to update username/password?',
  'component.user.loginMethodPassword.modificationMethod':
    'Please modify the users field in the /api/conf/conf.yaml file',
};
