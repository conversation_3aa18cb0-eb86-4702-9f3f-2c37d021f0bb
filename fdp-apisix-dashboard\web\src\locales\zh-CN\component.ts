/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.tagSelect.expand': '展开',
  'component.tagSelect.collapse': '收起',
  'component.tagSelect.all': '全部',
  'component.global.search': '查询',
  'component.global.reset': '重置',
  'component.global.confirm': '确认',
  'component.global.format': '格式化',
  'component.global.document': '文档',
  'component.global.enable': '启用',
  'component.global.disable': '禁用',
  'component.global.scope': '作用域',
  'component.global.example': '例子',
  'component.global.data.editor': '数据编辑器',
  'component.global.delete': '删除',
  'component.global.cancel': '取消',
  'component.global.submit': '提交',
  'component.global.create': '创建',
  'component.global.add': '新建',
  'component.global.save': '保存',
  'component.global.edit': '配置',
  'component.global.view': '查看',
  'component.global.duplicate': '复制',
  'component.global.manage': '管理',
  'component.global.update': '更新',
  'component.global.get': '获取',
  'component.global.edit.plugin': '配置插件',
  'component.global.loading': '加载中',
  'component.global.list': '列表',
  'component.global.description': '描述',
  'component.global.description.required': '请输入描述信息',
  'component.global.labels': '标签',
  'component.global.version': '路由版本',
  'component.global.operation': '操作',
  'component.status.success': '成功',
  'component.status.fail': '失败',
  'component.global.pleaseEnter': '请输入',
  'component.global.pleaseChoose': '请选择',
  'component.global.pleaseCheck': '请检查',
  'component.global.connectionTimeout': '连接超时时间',
  'component.global.sendTimeout': '发送超时时间',
  'component.global.receiveTimeout': '接收超时时间',
  'component.global.name': '名称',
  'component.global.id': 'ID',
  'component.global.updateTime': '更新时间',
  'component.global.create.consumer.success': '创建消费者成功',
  'component.global.delete.consumer.success': '删除消费者成功',
  'component.global.delete.routes.success': '删除路由成功',
  'component.global.edit.consumer.success': '配置消费者成功',

  'component.global.popconfirm.title.delete': '确定删除该条记录吗？',
  'component.global.steps.stepTitle.basicInformation': '基础信息',
  'component.global.steps.stepTitle.preview': '预览',
  'component.global.steps.stepTitle.pluginConfig': '插件配置',
  'component.global.input.ruleMessage.name': '仅支持字母、数字、- 和 _，且只能以字母开头',
  'component.global.form.itemExtraMessage.nameGloballyUnique': '消费者名称需全局唯一',
  'component.global.input.placeholder.description': '请输入路由描述（内容不超过 256 个字符）',

  // User component
  'component.user.loginByPassword': '账号密码登录',
  'component.user.login': '登录',

  'component.document': '操作手册',
  'component.label-manager': '标签管理器',

  'component.global.noConfigurationRequired': '无需配置',
  'component.global.copy': '复制',
  'component.global.copySuccess': '复制成功',
  'component.global.copyFail': '复制失败',

  'component.global.invalidYaml': '无效的 Yaml 数据',
  'component.global.invalidJson': '无效的 Json 数据',
};
