/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@import '~antd/es/style/themes/default.less';

html,
body,
#root {
  height: 100%;
  word-break: break-word;
  background-color: @layout-body-background;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.ant-pro-sider-light {
  z-index: 99;
}

.ant-checkbox-disabled > .ant-checkbox-inner,
.ant-radio-disabled > .ant-radio-inner,
.ant-input-number-disabled,
.ant-input[disabled] {
  background-color: #fff !important;
}

.ant-select-disabled.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  background-color: #fff !important;
}

.ant-layout.ant-layout-has-sider > .ant-layout,
.ant-layout.ant-layout-has-sider > .ant-layout-content {
  overflow-x: unset;
}

// NOTE: compatible with IconFont
.icon {
  width: 1.2em;
  height: 1.2em;
  margin-right: 1em;
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentcolor;
}

.ant-drawer-body > .ant-page-header {
  padding: 16px 12px;
}
